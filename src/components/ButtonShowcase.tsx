import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON><PERSON>, Check, Star } from 'lucide-react';
import { ReactButton } from './ReactButton';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import Editor from '@monaco-editor/react';
import buttonsData from '@/data/buttons.json';

interface ButtonData {
  id: string;
  name: string;
  description: string;
  category: string;
  variant: string;
  size: string;
  code: string;
  props: {
    variant: string;
    size: string;
    children: string;
    icon?: string;
  };
}

const ButtonShowcase: React.FC = () => {
  const [copiedId, setCopiedId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<{[key: string]: string}>({});

  const copyToClipboard = async (text: string, id: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopiedId(id);
      setTimeout(() => setCopiedId(null), 2000);
    } catch (err) {
      console.error('Failed to copy: ', err);
    }
  };

  const getTabValue = (buttonId: string) => {
    return activeTab[buttonId] || 'preview';
  };

  const setTabValue = (buttonId: string, value: string) => {
    setActiveTab(prev => ({ ...prev, [buttonId]: value }));
  };

  const renderButton = (buttonData: ButtonData) => {
    const { props } = buttonData;
    
    if (props.icon === 'Star') {
      return (
        <ReactButton variant={props.variant as any} size={props.size as any}>
          <Star className="w-4 h-4 mr-2" />
          {props.children}
        </ReactButton>
      );
    }
    
    return (
      <ReactButton variant={props.variant as any} size={props.size as any}>
        {props.children}
      </ReactButton>
    );
  };

  return (
    <section id="button-showcase" className="py-20 px-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
            Professional Button Collection
          </h2>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Production-ready React + Tailwind button components. Copy and paste into your project.
          </p>
        </motion.div>

        {/* Button Grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {buttonsData.map((buttonData, index) => (
            <motion.div
              key={buttonData.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: index * 0.05 }}
              className="bg-card border border-border rounded-xl overflow-hidden hover:shadow-medium transition-all duration-300 hover:border-border-hover group"
            >
              <Tabs 
                value={getTabValue(buttonData.id)} 
                onValueChange={(value) => setTabValue(buttonData.id, value)}
                className="w-full"
              >
                <TabsList className="w-full grid grid-cols-2 bg-background-alt m-0 rounded-none h-12">
                  <TabsTrigger value="preview" className="data-[state=active]:bg-background">
                    Preview
                  </TabsTrigger>
                  <TabsTrigger value="code" className="data-[state=active]:bg-background">
                    Code
                  </TabsTrigger>
                </TabsList>
                
                <div className="p-6">
                  <div className="relative min-h-[120px]">
                    <AnimatePresence mode="wait">
                      <TabsContent value="preview" className="mt-0">
                        <motion.div
                          key="preview"
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: 20 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="flex items-center justify-center p-8 bg-background-alt rounded-lg border border-border"
                        >
                          {renderButton(buttonData)}
                        </motion.div>
                      </TabsContent>

                      <TabsContent value="code" className="mt-0">
                        <motion.div
                          key="code"
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          transition={{ duration: 0.3, ease: "easeInOut" }}
                          className="relative"
                        >
                          <div className="relative bg-background-alt rounded-lg border border-border overflow-hidden">
                            <Editor
                              height="120px"
                              defaultLanguage="javascript"
                              value={buttonData.code}
                              theme="vs-dark"
                              options={{
                                readOnly: false,
                                minimap: { enabled: false },
                                scrollBeyondLastLine: false,
                                fontSize: 12,
                                lineNumbers: 'off',
                                folding: false,
                                wordWrap: 'on',
                                automaticLayout: true,
                                padding: { top: 10, bottom: 10 }
                              }}
                            />
                            <button
                              onClick={() => copyToClipboard(buttonData.code, buttonData.id)}
                              className="absolute top-2 right-2 p-2 rounded-md bg-muted hover:bg-muted-hover transition-colors duration-200 z-10"
                              title="Copy code"
                            >
                              {copiedId === buttonData.id ? (
                                <Check className="w-4 h-4 text-green-600" />
                              ) : (
                                <Copy className="w-4 h-4" />
                              )}
                            </button>
                          </div>
                        </motion.div>
                      </TabsContent>
                    </AnimatePresence>
                  </div>
                </div>
              </Tabs>
            </motion.div>
          ))}
        </motion.div>

      </div>
    </section>
  );
};

export default ButtonShowcase;
