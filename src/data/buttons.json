[{"id": "primary-default", "name": "Primary Button", "description": "The main call-to-action button for primary actions", "category": "Primary", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Click me</Button>", "props": {"variant": "primary", "size": "default", "children": "Click me"}}, {"id": "primary-large", "name": "Primary Large", "description": "Larger primary button for hero sections", "category": "Primary", "variant": "primary", "size": "lg", "code": "<Button variant=\"primary\" size=\"lg\">Get Started</Button>", "props": {"variant": "primary", "size": "lg", "children": "Get Started"}}, {"id": "secondary-default", "name": "Secondary Button", "description": "Secondary actions and complementary buttons", "category": "Secondary", "variant": "secondary", "size": "default", "code": "<Button variant=\"secondary\">Learn More</Button>", "props": {"variant": "secondary", "size": "default", "children": "Learn More"}}, {"id": "outline-default", "name": "Outline Button", "description": "Subtle button with border for secondary actions", "category": "Outline", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Cancel</Button>", "props": {"variant": "outline", "size": "default", "children": "Cancel"}}, {"id": "ghost-default", "name": "<PERSON>", "description": "Minimal button for subtle interactions", "category": "Ghost", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">Skip</Button>", "props": {"variant": "ghost", "size": "default", "children": "<PERSON><PERSON>"}}, {"id": "destructive-default", "name": "Destructive But<PERSON>", "description": "For dangerous actions like delete or remove", "category": "Destructive", "variant": "destructive", "size": "default", "code": "<Button variant=\"destructive\">Delete</Button>", "props": {"variant": "destructive", "size": "default", "children": "Delete"}}, {"id": "gradient-primary", "name": "<PERSON><PERSON><PERSON>", "description": "Eye-catching gradient button for special actions", "category": "Special", "variant": "gradient", "size": "default", "code": "<Button variant=\"gradient\">Upgrade Now</Button>", "props": {"variant": "gradient", "size": "default", "children": "Upgrade Now"}}, {"id": "icon-button", "name": "Icon <PERSON>", "description": "Button with icon for enhanced UX", "category": "Special", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">\n  <Star className=\"w-4 h-4 mr-2\" />\n  Star this repo\n</Button>", "props": {"variant": "primary", "size": "default", "icon": "Star", "children": "Star this repo"}}, {"id": "gradient-primary", "name": "Gradient Primary", "description": "Eye-catching gradient button for main actions", "category": "Gradient", "variant": "gradient", "size": "default", "code": "<Button variant=\"gradient\">Get Started</Button>", "props": {"variant": "gradient", "size": "default", "children": "Get Started"}}, {"id": "gradient-large", "name": "Gradient Large", "description": "Large gradient button for hero sections", "category": "Gradient", "variant": "gradient", "size": "lg", "code": "<Button variant=\"gradient\" size=\"lg\">Start Free Trial</Button>", "props": {"variant": "gradient", "size": "lg", "children": "Start Free Trial"}}, {"id": "success-default", "name": "Success Button", "description": "Green success button for positive actions", "category": "Status", "variant": "success", "size": "default", "code": "<Button variant=\"success\">Save Changes</Button>", "props": {"variant": "success", "size": "default", "children": "Save Changes"}}, {"id": "danger-default", "name": "<PERSON>", "description": "Red danger button for destructive actions", "category": "Status", "variant": "destructive", "size": "default", "code": "<Button variant=\"destructive\">Delete Account</Button>", "props": {"variant": "destructive", "size": "default", "children": "Delete Account"}}, {"id": "warning-default", "name": "Warning Button", "description": "Orange warning button for caution actions", "category": "Status", "variant": "warning", "size": "default", "code": "<Button variant=\"warning\">Proceed with Caution</Button>", "props": {"variant": "warning", "size": "default", "children": "Proceed with Caution"}}, {"id": "info-default", "name": "Info Button", "description": "Blue info button for informational actions", "category": "Status", "variant": "info", "size": "default", "code": "<Button variant=\"info\">Learn More</Button>", "props": {"variant": "info", "size": "default", "children": "Learn More"}}, {"id": "rounded-primary", "name": "Rounded Primary", "description": "Fully rounded primary button", "category": "Rounded", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\" className=\"rounded-full\">Subscribe</Button>", "props": {"variant": "primary", "size": "default", "children": "Subscribe"}}, {"id": "rounded-outline", "name": "Rounded Outline", "description": "Fully rounded outline button", "category": "Rounded", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\" className=\"rounded-full\">Follow</Button>", "props": {"variant": "outline", "size": "default", "children": "Follow"}}, {"id": "small-primary", "name": "Small Primary", "description": "Compact primary button for tight spaces", "category": "Size", "variant": "primary", "size": "sm", "code": "<Button variant=\"primary\" size=\"sm\">Apply</Button>", "props": {"variant": "primary", "size": "sm", "children": "Apply"}}, {"id": "small-secondary", "name": "Small Secondary", "description": "Compact secondary button", "category": "Size", "variant": "secondary", "size": "sm", "code": "<Button variant=\"secondary\" size=\"sm\">Cancel</Button>", "props": {"variant": "secondary", "size": "sm", "children": "Cancel"}}, {"id": "large-primary", "name": "Large Primary", "description": "Large primary button for emphasis", "category": "Size", "variant": "primary", "size": "lg", "code": "<Button variant=\"primary\" size=\"lg\">Download Now</Button>", "props": {"variant": "primary", "size": "lg", "children": "Download Now"}}, {"id": "large-secondary", "name": "Large Secondary", "description": "Large secondary button", "category": "Size", "variant": "secondary", "size": "lg", "code": "<Button variant=\"secondary\" size=\"lg\">View Details</Button>", "props": {"variant": "secondary", "size": "lg", "children": "View Details"}}, {"id": "ghost-hover", "name": "Ghost Hover", "description": "Subtle ghost button with hover effect", "category": "Ghost", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">Settings</Button>", "props": {"variant": "ghost", "size": "default", "children": "Settings"}}, {"id": "link-button", "name": "<PERSON>", "description": "<PERSON><PERSON> styled as a link", "category": "Link", "variant": "link", "size": "default", "code": "<Button variant=\"link\">Read Documentation</Button>", "props": {"variant": "link", "size": "default", "children": "Read Documentation"}}, {"id": "icon-only-sm", "name": "Small Icon Only", "description": "Small icon-only button", "category": "Icon", "variant": "outline", "size": "icon", "code": "<Button variant=\"outline\" size=\"icon\"><Star className=\"w-4 h-4\" /></Button>", "props": {"variant": "outline", "size": "icon", "children": "★"}}, {"id": "loading-primary", "name": "Loading Primary", "description": "Primary button with loading state", "category": "Loading", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\" disabled>Loading...</Button>", "props": {"variant": "primary", "size": "default", "children": "Loading..."}}, {"id": "disabled-secondary", "name": "Disabled Secondary", "description": "Disabled secondary button", "category": "State", "variant": "secondary", "size": "default", "code": "<Button variant=\"secondary\" disabled>Unavailable</Button>", "props": {"variant": "secondary", "size": "default", "children": "Unavailable"}}, {"id": "cta-gradient", "name": "CTA Gradient", "description": "Call-to-action gradient button", "category": "CTA", "variant": "gradient", "size": "lg", "code": "<Button variant=\"gradient\" size=\"lg\">Start Your Journey</Button>", "props": {"variant": "gradient", "size": "lg", "children": "Start Your Journey"}}, {"id": "social-github", "name": "GitHub Social", "description": "GitHub-themed social button", "category": "Social", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Continue with GitHub</Button>", "props": {"variant": "outline", "size": "default", "children": "Continue with GitHub"}}, {"id": "social-google", "name": "Google Social", "description": "Google-themed social button", "category": "Social", "variant": "secondary", "size": "default", "code": "<Button variant=\"secondary\">Sign in with Google</Button>", "props": {"variant": "secondary", "size": "default", "children": "Sign in with Google"}}, {"id": "payment-primary", "name": "Payment Primary", "description": "Primary payment button", "category": "Payment", "variant": "primary", "size": "lg", "code": "<Button variant=\"primary\" size=\"lg\">Complete Purchase</Button>", "props": {"variant": "primary", "size": "lg", "children": "Complete Purchase"}}, {"id": "payment-secure", "name": "Secure Payment", "description": "Secure payment button with emphasis", "category": "Payment", "variant": "success", "size": "lg", "code": "<Button variant=\"success\" size=\"lg\">Pay Securely</Button>", "props": {"variant": "success", "size": "lg", "children": "Pay Securely"}}, {"id": "form-submit", "name": "Form Submit", "description": "Standard form submission button", "category": "Form", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\" type=\"submit\">Submit Form</Button>", "props": {"variant": "primary", "size": "default", "children": "Submit Form"}}, {"id": "form-reset", "name": "Form Reset", "description": "Form reset button", "category": "Form", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\" type=\"reset\">Reset</Button>", "props": {"variant": "outline", "size": "default", "children": "Reset"}}, {"id": "navigation-back", "name": "Navigation Back", "description": "Back navigation button", "category": "Navigation", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">&larr; Back</Button>", "props": {"variant": "ghost", "size": "default", "children": "← Back"}}, {"id": "navigation-next", "name": "Navigation Next", "description": "Next navigation button", "category": "Navigation", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Next &rarr;</Button>", "props": {"variant": "primary", "size": "default", "children": "Next →"}}, {"id": "modal-close", "name": "Modal Close", "description": "Close button for modals", "category": "Modal", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">&times;</Button>", "props": {"variant": "ghost", "size": "sm", "children": "×"}}, {"id": "modal-confirm", "name": "Modal Confirm", "description": "Confirmation button for modals", "category": "Modal", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Confirm</Button>", "props": {"variant": "primary", "size": "default", "children": "Confirm"}}, {"id": "modal-cancel", "name": "Modal Cancel", "description": "Cancel button for modals", "category": "Modal", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Cancel</Button>", "props": {"variant": "outline", "size": "default", "children": "Cancel"}}, {"id": "dropdown-trigger", "name": "Dropdown Trigger", "description": "Button to trigger dropdown menus", "category": "Dropdown", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Options ↓</Button>", "props": {"variant": "outline", "size": "default", "children": "Options ↓"}}, {"id": "filter-active", "name": "Filter Active", "description": "Active filter button", "category": "Filter", "variant": "primary", "size": "sm", "code": "<Button variant=\"primary\" size=\"sm\">Active</Button>", "props": {"variant": "primary", "size": "sm", "children": "Active"}}, {"id": "filter-inactive", "name": "Filter Inactive", "description": "Inactive filter button", "category": "Filter", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">All</Button>", "props": {"variant": "ghost", "size": "sm", "children": "All"}}, {"id": "tag-removable", "name": "Removable Tag", "description": "Tag button with remove option", "category": "Tag", "variant": "secondary", "size": "sm", "code": "<Button variant=\"secondary\" size=\"sm\">React &times;</Button>", "props": {"variant": "secondary", "size": "sm", "children": "React ×"}}, {"id": "notification-dismiss", "name": "Notification Dismiss", "description": "Dismiss notification button", "category": "Notification", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">Dismiss</Button>", "props": {"variant": "ghost", "size": "sm", "children": "<PERSON><PERSON><PERSON>"}}, {"id": "notification-action", "name": "Notification Action", "description": "Action button in notifications", "category": "Notification", "variant": "primary", "size": "sm", "code": "<Button variant=\"primary\" size=\"sm\">View</Button>", "props": {"variant": "primary", "size": "sm", "children": "View"}}, {"id": "toolbar-bold", "name": "Toolbar Bold", "description": "Bold formatting button", "category": "<PERSON><PERSON><PERSON>", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">B</Button>", "props": {"variant": "ghost", "size": "sm", "children": "B"}}, {"id": "toolbar-italic", "name": "<PERSON><PERSON>bar Italic", "description": "Italic formatting button", "category": "<PERSON><PERSON><PERSON>", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">I</Button>", "props": {"variant": "ghost", "size": "sm", "children": "I"}}, {"id": "toolbar-underline", "name": "Toolbar Underline", "description": "Underline formatting button", "category": "<PERSON><PERSON><PERSON>", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">U</Button>", "props": {"variant": "ghost", "size": "sm", "children": "U"}}, {"id": "media-play", "name": "Media Play", "description": "Play button for media controls", "category": "Media", "variant": "primary", "size": "icon", "code": "<Button variant=\"primary\" size=\"icon\">Play</Button>", "props": {"variant": "primary", "size": "icon", "children": "Play"}}, {"id": "media-pause", "name": "Media Pause", "description": "Pause button for media controls", "category": "Media", "variant": "secondary", "size": "icon", "code": "<Button variant=\"secondary\" size=\"icon\">Pause</Button>", "props": {"variant": "secondary", "size": "icon", "children": "Pause"}}, {"id": "media-stop", "name": "Media Stop", "description": "Stop button for media controls", "category": "Media", "variant": "outline", "size": "icon", "code": "<Button variant=\"outline\" size=\"icon\">Stop</Button>", "props": {"variant": "outline", "size": "icon", "children": "Stop"}}, {"id": "search-submit", "name": "Search Submit", "description": "Search form submission button", "category": "Search", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Search</Button>", "props": {"variant": "primary", "size": "default", "children": "Search"}}, {"id": "search-clear", "name": "Search Clear", "description": "Clear search button", "category": "Search", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">Clear</Button>", "props": {"variant": "ghost", "size": "sm", "children": "Clear"}}, {"id": "pagination-prev", "name": "Pagination Previous", "description": "Previous page button", "category": "Pagination", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Previous</Button>", "props": {"variant": "outline", "size": "default", "children": "Previous"}}, {"id": "pagination-next", "name": "Pagination Next", "description": "Next page button", "category": "Pagination", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Next</Button>", "props": {"variant": "outline", "size": "default", "children": "Next"}}, {"id": "pagination-number", "name": "Pagination Number", "description": "Page number button", "category": "Pagination", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">1</Button>", "props": {"variant": "ghost", "size": "default", "children": "1"}}, {"id": "pagination-active", "name": "Pagination Active", "description": "Active page number button", "category": "Pagination", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">2</Button>", "props": {"variant": "primary", "size": "default", "children": "2"}}, {"id": "tab-active", "name": "Tab Active", "description": "Active tab button", "category": "Tab", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Overview</Button>", "props": {"variant": "primary", "size": "default", "children": "Overview"}}, {"id": "tab-inactive", "name": "Tab Inactive", "description": "Inactive tab button", "category": "Tab", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">Details</Button>", "props": {"variant": "ghost", "size": "default", "children": "Details"}}, {"id": "breadcrumb-link", "name": "Breadcrumb Link", "description": "Breadcrumb navigation link", "category": "Breadcrumb", "variant": "link", "size": "sm", "code": "<Button variant=\"link\" size=\"sm\">Home</Button>", "props": {"variant": "link", "size": "sm", "children": "Home"}}, {"id": "breadcrumb-current", "name": "Breadcrumb Current", "description": "Current breadcrumb item", "category": "Breadcrumb", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\" disabled>Current</Button>", "props": {"variant": "ghost", "size": "sm", "children": "Current"}}, {"id": "card-action", "name": "Card Action", "description": "Action button in cards", "category": "Card", "variant": "outline", "size": "sm", "code": "<Button variant=\"outline\" size=\"sm\">View More</Button>", "props": {"variant": "outline", "size": "sm", "children": "View More"}}, {"id": "card-primary", "name": "Card Primary", "description": "Primary action in cards", "category": "Card", "variant": "primary", "size": "sm", "code": "<Button variant=\"primary\" size=\"sm\">Select</Button>", "props": {"variant": "primary", "size": "sm", "children": "Select"}}, {"id": "floating-action", "name": "Floating Action", "description": "Floating action button", "category": "Floating", "variant": "primary", "size": "icon", "code": "<Button variant=\"primary\" size=\"icon\" className=\"rounded-full\">+</Button>", "props": {"variant": "primary", "size": "icon", "children": "+"}}, {"id": "toggle-on", "name": "Toggle On", "description": "Toggle button in on state", "category": "Toggle", "variant": "primary", "size": "sm", "code": "<Button variant=\"primary\" size=\"sm\">ON</Button>", "props": {"variant": "primary", "size": "sm", "children": "ON"}}, {"id": "toggle-off", "name": "Toggle Off", "description": "Toggle button in off state", "category": "Toggle", "variant": "outline", "size": "sm", "code": "<Button variant=\"outline\" size=\"sm\">OFF</Button>", "props": {"variant": "outline", "size": "sm", "children": "OFF"}}, {"id": "menu-item", "name": "<PERSON><PERSON>", "description": "Menu item button", "category": "<PERSON><PERSON>", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\" className=\"justify-start\">Profile</Button>", "props": {"variant": "ghost", "size": "default", "children": "Profile"}}, {"id": "menu-destructive", "name": "Menu Destructive", "description": "Destructive menu item", "category": "<PERSON><PERSON>", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\" className=\"justify-start text-destructive\">Delete</Button>", "props": {"variant": "ghost", "size": "default", "children": "Delete"}}, {"id": "auth-login", "name": "<PERSON><PERSON>", "description": "User login button", "category": "<PERSON><PERSON>", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Log In</Button>", "props": {"variant": "primary", "size": "default", "children": "Log In"}}, {"id": "auth-signup", "name": "Sign Up <PERSON><PERSON>", "description": "User registration button", "category": "<PERSON><PERSON>", "variant": "secondary", "size": "default", "code": "<Button variant=\"secondary\">Sign Up</Button>", "props": {"variant": "secondary", "size": "default", "children": "Sign Up"}}, {"id": "auth-logout", "name": "<PERSON><PERSON><PERSON>", "description": "User logout button", "category": "<PERSON><PERSON>", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Log Out</Button>", "props": {"variant": "outline", "size": "default", "children": "Log Out"}}, {"id": "file-upload", "name": "File Upload", "description": "File upload button", "category": "File", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Upload File</Button>", "props": {"variant": "outline", "size": "default", "children": "Upload File"}}, {"id": "file-download", "name": "File Download", "description": "File download button", "category": "File", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Download</Button>", "props": {"variant": "primary", "size": "default", "children": "Download"}}, {"id": "file-delete", "name": "File Delete", "description": "File deletion button", "category": "File", "variant": "destructive", "size": "sm", "code": "<Button variant=\"destructive\" size=\"sm\">Delete File</Button>", "props": {"variant": "destructive", "size": "sm", "children": "Delete File"}}, {"id": "share-button", "name": "Share Button", "description": "Content sharing button", "category": "Share", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Share</Button>", "props": {"variant": "outline", "size": "default", "children": "Share"}}, {"id": "copy-link", "name": "Copy Link", "description": "Copy link to clipboard", "category": "Share", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">Copy Link</Button>", "props": {"variant": "ghost", "size": "sm", "children": "Copy Link"}}, {"id": "bookmark-add", "name": "Add Bookmark", "description": "Add to bookmarks button", "category": "Bookmark", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">Bookmark</Button>", "props": {"variant": "ghost", "size": "default", "children": "Bookmark"}}, {"id": "bookmark-remove", "name": "Remove Bookmark", "description": "Remove from bookmarks", "category": "Bookmark", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Bookmarked</Button>", "props": {"variant": "primary", "size": "default", "children": "Bookmarked"}}, {"id": "like-button", "name": "Like <PERSON><PERSON>", "description": "Like/heart button", "category": "Social", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">♡ Like</Button>", "props": {"variant": "ghost", "size": "sm", "children": "♡ Like"}}, {"id": "like-active", "name": "Like Active", "description": "Active like button", "category": "Social", "variant": "primary", "size": "sm", "code": "<Button variant=\"primary\" size=\"sm\">♥ Liked</Button>", "props": {"variant": "primary", "size": "sm", "children": "♥ Liked"}}, {"id": "comment-button", "name": "Comment <PERSON>", "description": "Add comment button", "category": "Social", "variant": "outline", "size": "sm", "code": "<Button variant=\"outline\" size=\"sm\">Comment</Button>", "props": {"variant": "outline", "size": "sm", "children": "Comment"}}, {"id": "follow-button", "name": "Follow <PERSON><PERSON>", "description": "Follow user button", "category": "Social", "variant": "primary", "size": "sm", "code": "<Button variant=\"primary\" size=\"sm\">Follow</Button>", "props": {"variant": "primary", "size": "sm", "children": "Follow"}}, {"id": "unfollow-button", "name": "Unfollow Button", "description": "Unfollow user button", "category": "Social", "variant": "outline", "size": "sm", "code": "<Button variant=\"outline\" size=\"sm\">Following</Button>", "props": {"variant": "outline", "size": "sm", "children": "Following"}}, {"id": "subscribe-newsletter", "name": "Subscribe Newsletter", "description": "Newsletter subscription button", "category": "Newsletter", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Subscribe</Button>", "props": {"variant": "primary", "size": "default", "children": "Subscribe"}}, {"id": "unsubscribe", "name": "Unsubscribe", "description": "Unsubscribe button", "category": "Newsletter", "variant": "outline", "size": "sm", "code": "<Button variant=\"outline\" size=\"sm\">Unsubscribe</Button>", "props": {"variant": "outline", "size": "sm", "children": "Unsubscribe"}}, {"id": "contact-us", "name": "Contact Us", "description": "Contact form button", "category": "Contact", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Contact Us</Button>", "props": {"variant": "primary", "size": "default", "children": "Contact Us"}}, {"id": "get-quote", "name": "Get Quote", "description": "Request quote button", "category": "Contact", "variant": "secondary", "size": "default", "code": "<Button variant=\"secondary\">Get Quote</Button>", "props": {"variant": "secondary", "size": "default", "children": "Get Quote"}}, {"id": "schedule-demo", "name": "Schedule Demo", "description": "Schedule demo button", "category": "Contact", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Schedule Demo</Button>", "props": {"variant": "outline", "size": "default", "children": "Schedule Demo"}}, {"id": "add-to-cart", "name": "Add to Cart", "description": "Add product to cart", "category": "Ecommerce", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Add to Cart</Button>", "props": {"variant": "primary", "size": "default", "children": "Add to Cart"}}, {"id": "buy-now", "name": "Buy Now", "description": "Immediate purchase button", "category": "Ecommerce", "variant": "success", "size": "lg", "code": "<Button variant=\"success\" size=\"lg\">Buy Now</Button>", "props": {"variant": "success", "size": "lg", "children": "Buy Now"}}, {"id": "wishlist-add", "name": "Add to Wishlist", "description": "Add to wishlist button", "category": "Ecommerce", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Add to Wishlist</Button>", "props": {"variant": "outline", "size": "default", "children": "Add to Wishlist"}}, {"id": "compare-add", "name": "Add to Compare", "description": "Add to comparison button", "category": "Ecommerce", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">Compare</Button>", "props": {"variant": "ghost", "size": "sm", "children": "Compare"}}, {"id": "checkout-button", "name": "Checkout", "description": "Proceed to checkout", "category": "Ecommerce", "variant": "primary", "size": "lg", "code": "<Button variant=\"primary\" size=\"lg\">Checkout</Button>", "props": {"variant": "primary", "size": "lg", "children": "Checkout"}}, {"id": "view-cart", "name": "View Cart", "description": "View shopping cart", "category": "Ecommerce", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">View Cart</Button>", "props": {"variant": "outline", "size": "default", "children": "View Cart"}}, {"id": "quick-view", "name": "Quick View", "description": "Quick product view", "category": "Ecommerce", "variant": "secondary", "size": "sm", "code": "<Button variant=\"secondary\" size=\"sm\">Quick View</Button>", "props": {"variant": "secondary", "size": "sm", "children": "Quick View"}}, {"id": "edit-profile", "name": "Edit Profile", "description": "Edit user profile", "category": "Profile", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Edit Profile</Button>", "props": {"variant": "outline", "size": "default", "children": "Edit Profile"}}, {"id": "change-password", "name": "Change Password", "description": "Change user password", "category": "Profile", "variant": "secondary", "size": "default", "code": "<Button variant=\"secondary\">Change Password</Button>", "props": {"variant": "secondary", "size": "default", "children": "Change Password"}}, {"id": "delete-account", "name": "Delete Account", "description": "Delete user account", "category": "Profile", "variant": "destructive", "size": "default", "code": "<Button variant=\"destructive\">Delete Account</Button>", "props": {"variant": "destructive", "size": "default", "children": "Delete Account"}}, {"id": "privacy-settings", "name": "Privacy Settings", "description": "Manage privacy settings", "category": "Profile", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">Privacy Settings</Button>", "props": {"variant": "ghost", "size": "default", "children": "Privacy Settings"}}, {"id": "notification-settings", "name": "Notification Settings", "description": "Manage notifications", "category": "Profile", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">Notifications</Button>", "props": {"variant": "ghost", "size": "default", "children": "Notifications"}}, {"id": "help-center", "name": "Help Center", "description": "Access help center", "category": "Help", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Help Center</Button>", "props": {"variant": "outline", "size": "default", "children": "Help Center"}}, {"id": "support-chat", "name": "Support Chat", "description": "Start support chat", "category": "Help", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Chat Support</Button>", "props": {"variant": "primary", "size": "default", "children": "Chat Support"}}, {"id": "report-bug", "name": "Report Bug", "description": "Report a bug", "category": "Help", "variant": "secondary", "size": "sm", "code": "<Button variant=\"secondary\" size=\"sm\">Report Bug</Button>", "props": {"variant": "secondary", "size": "sm", "children": "Report Bug"}}, {"id": "feedback", "name": "<PERSON><PERSON><PERSON>", "description": "Provide feedback", "category": "Help", "variant": "ghost", "size": "sm", "code": "<Button variant=\"ghost\" size=\"sm\">Feedback</Button>", "props": {"variant": "ghost", "size": "sm", "children": "<PERSON><PERSON><PERSON>"}}, {"id": "faq-button", "name": "FAQ", "description": "View frequently asked questions", "category": "Help", "variant": "link", "size": "default", "code": "<Button variant=\"link\">FAQ</Button>", "props": {"variant": "link", "size": "default", "children": "FAQ"}}]