[{"id": "primary-default", "name": "Primary Button", "description": "The main call-to-action button for primary actions", "category": "Primary", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">Click me</Button>", "props": {"variant": "primary", "size": "default", "children": "Click me"}}, {"id": "primary-large", "name": "Primary Large", "description": "Larger primary button for hero sections", "category": "Primary", "variant": "primary", "size": "lg", "code": "<Button variant=\"primary\" size=\"lg\">Get Started</Button>", "props": {"variant": "primary", "size": "lg", "children": "Get Started"}}, {"id": "secondary-default", "name": "Secondary Button", "description": "Secondary actions and complementary buttons", "category": "Secondary", "variant": "secondary", "size": "default", "code": "<Button variant=\"secondary\">Learn More</Button>", "props": {"variant": "secondary", "size": "default", "children": "Learn More"}}, {"id": "outline-default", "name": "Outline Button", "description": "Subtle button with border for secondary actions", "category": "Outline", "variant": "outline", "size": "default", "code": "<Button variant=\"outline\">Cancel</Button>", "props": {"variant": "outline", "size": "default", "children": "Cancel"}}, {"id": "ghost-default", "name": "<PERSON>", "description": "Minimal button for subtle interactions", "category": "Ghost", "variant": "ghost", "size": "default", "code": "<Button variant=\"ghost\">Skip</Button>", "props": {"variant": "ghost", "size": "default", "children": "<PERSON><PERSON>"}}, {"id": "destructive-default", "name": "Destructive But<PERSON>", "description": "For dangerous actions like delete or remove", "category": "Destructive", "variant": "destructive", "size": "default", "code": "<Button variant=\"destructive\">Delete</Button>", "props": {"variant": "destructive", "size": "default", "children": "Delete"}}, {"id": "gradient-primary", "name": "<PERSON><PERSON><PERSON>", "description": "Eye-catching gradient button for special actions", "category": "Special", "variant": "gradient", "size": "default", "code": "<Button variant=\"gradient\">Upgrade Now</Button>", "props": {"variant": "gradient", "size": "default", "children": "Upgrade Now"}}, {"id": "icon-button", "name": "Icon <PERSON>", "description": "Button with icon for enhanced UX", "category": "Special", "variant": "primary", "size": "default", "code": "<Button variant=\"primary\">\n  <Star className=\"w-4 h-4 mr-2\" />\n  Star this repo\n</Button>", "props": {"variant": "primary", "size": "default", "icon": "Star", "children": "Star this repo"}}]