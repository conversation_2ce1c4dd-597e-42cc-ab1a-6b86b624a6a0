@tailwind base;
@tailwind components;
@tailwind utilities;

/* ReactButtons Design System - Professional theming with CSS variables */

@layer base {
  :root {
    /* Core Brand Colors */
    --primary: 217 91% 60%;
    --primary-hover: 217 91% 55%;
    --primary-foreground: 0 0% 100%;
    
    /* Secondary & Neutral Colors */
    --secondary: 210 40% 98%;
    --secondary-hover: 210 40% 95%;
    --secondary-foreground: 222.2 84% 4.9%;
    
    /* Background System */
    --background: 0 0% 100%;
    --background-alt: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;
    
    /* Component Colors */
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --card-hover: 210 40% 99%;
    
    /* Interactive Elements */
    --muted: 210 40% 96.1%;
    --muted-hover: 210 40% 93%;
    --muted-foreground: 215.4 16.3% 46.9%;
    
    --accent: 217 91% 60%;
    --accent-foreground: 0 0% 100%;
    
    /* Status Colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 100%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 100%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    
    /* Borders & Inputs */
    --border: 214.3 31.8% 91.4%;
    --border-hover: 214.3 31.8% 85%;
    --input: 214.3 31.8% 91.4%;
    --ring: 217 91% 60%;
    
    /* Button System */
    --button-primary-bg: var(--primary);
    --button-primary-hover: var(--primary-hover);
    --button-primary-text: var(--primary-foreground);
    
    --button-secondary-bg: var(--secondary);
    --button-secondary-hover: var(--secondary-hover);
    --button-secondary-text: var(--secondary-foreground);
    
    --button-outline-bg: transparent;
    --button-outline-hover: var(--muted);
    --button-outline-border: var(--border);
    --button-outline-text: var(--foreground);
    
    --button-ghost-bg: transparent;
    --button-ghost-hover: var(--muted);
    --button-ghost-text: var(--foreground);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--background-alt)));
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 hsl(var(--primary) / 0.05);
    --shadow-md: 0 4px 6px -1px hsl(var(--primary) / 0.1), 0 2px 4px -1px hsl(var(--primary) / 0.06);
    --shadow-lg: 0 10px 15px -3px hsl(var(--primary) / 0.1), 0 4px 6px -2px hsl(var(--primary) / 0.05);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.15);
    
    /* Layout */
    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Core Brand Colors */
    --primary: 217 91% 65%;
    --primary-hover: 217 91% 70%;
    --primary-foreground: 222.2 84% 4.9%;
    
    /* Background System */
    --background: 222.2 84% 4.9%;
    --background-alt: 217.2 32.6% 17.5%;
    --foreground: 210 40% 98%;
    
    /* Component Colors */
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --card-hover: 217.2 32.6% 17.5%;
    
    /* Secondary & Neutral Colors */
    --secondary: 217.2 32.6% 17.5%;
    --secondary-hover: 217.2 32.6% 20%;
    --secondary-foreground: 210 40% 98%;
    
    /* Interactive Elements */
    --muted: 217.2 32.6% 17.5%;
    --muted-hover: 217.2 32.6% 20%;
    --muted-foreground: 215 20.2% 65.1%;
    
    --accent: 217 91% 65%;
    --accent-foreground: 222.2 84% 4.9%;
    
    /* Status Colors */
    --success: 142 76% 45%;
    --warning: 38 92% 60%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    
    /* Borders & Inputs */
    --border: 217.2 32.6% 17.5%;
    --border-hover: 217.2 32.6% 25%;
    --input: 217.2 32.6% 17.5%;
    --ring: 217 91% 65%;
    
    /* Button System Updates for Dark Mode */
    --button-primary-bg: var(--primary);
    --button-primary-hover: var(--primary-hover);
    --button-primary-text: var(--primary-foreground);
    
    --button-secondary-bg: var(--secondary);
    --button-secondary-hover: var(--secondary-hover);
    --button-secondary-text: var(--secondary-foreground);
    
    --button-outline-hover: var(--muted);
    --button-outline-text: var(--foreground);
    
    --button-ghost-hover: var(--muted);
    --button-ghost-text: var(--foreground);
    
    /* Gradients for Dark Mode */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    --gradient-subtle: linear-gradient(180deg, hsl(var(--background)), hsl(var(--background-alt)));
    
    /* Shadows for Dark Mode */
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.25);
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Animation utilities */
  .animate-fade-up {
    @apply animate-[fade-up_0.5s_ease-out];
  }
  
  .animate-slide-in {
    @apply animate-[slide-in_0.3s_ease-out];
  }
  
  /* Interactive elements */
  .interactive-scale {
    @apply transition-transform duration-200 hover:scale-[1.02] active:scale-[0.98];
  }
  
  .glass-effect {
    @apply backdrop-blur-sm bg-white/80 dark:bg-gray-900/80;
  }
  
  /* Code styling */
  .code-block {
    @apply bg-muted/50 border border-border rounded-lg p-4 font-mono text-sm;
  }
}

@layer utilities {
  /* Custom button variants using CSS variables */
  .btn-primary {
    @apply bg-[var(--button-primary-bg)] text-[var(--button-primary-text)] hover:bg-[var(--button-primary-hover)];
  }
  
  .btn-secondary {
    @apply bg-[var(--button-secondary-bg)] text-[var(--button-secondary-text)] hover:bg-[var(--button-secondary-hover)];
  }
  
  .btn-outline {
    @apply bg-[var(--button-outline-bg)] text-[var(--button-outline-text)] border border-[var(--button-outline-border)] hover:bg-[var(--button-outline-hover)];
  }
  
  .btn-ghost {
    @apply bg-[var(--button-ghost-bg)] text-[var(--button-ghost-text)] hover:bg-[var(--button-ghost-hover)];
  }
}